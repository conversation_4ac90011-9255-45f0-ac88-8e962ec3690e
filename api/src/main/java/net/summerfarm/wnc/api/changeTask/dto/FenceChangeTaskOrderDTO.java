package net.summerfarm.wnc.api.changeTask.dto;

import lombok.Data;
import net.summerfarm.wnc.common.enums.FenceChangeTaskDetailEnums;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description:切仓任务订单明细数据转换对象
 * date: 2023/8/24 15:24
 *
 * <AUTHOR>
 */
@Data
public class FenceChangeTaskOrderDTO implements Serializable {

    private static final long serialVersionUID = 3668020663842466446L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 切仓任务ID
     */
    private Long taskId;

    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 来源，200：鲜沐-订单，201：鲜沐-售后，202：鲜沐-样品，203：鲜沐-省心送，210：saas-订单，211：saas-售后
     */
    private Integer source;

    /**
     * 来源描述
     */
    private String sourceDesc;

    /**
     * 外部联系人ID
     */
    private String outerContactId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 状态，10：待处理，20：处理成功，30：处理失败
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 失败原因
     */
    private String remark;

    /**
     * 履约确认时间
     */
    private LocalDateTime fulfillConfirmTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 原城配仓编号
     */
    private Integer oldStoreNo;

    /**
     * 新城配仓编号
     */
    private Integer newStoreNo;

    /**
     * 旧区域编号
     */
    private Integer oldAreaNo;

    /**
     * 新区域编号
     */
    private Integer newAreaNo;

    /**
     * 新围栏ID
     */
    private Integer newFenceId;

    /**
     * 旧围栏ID
     */
    private Integer oldFenceId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 旧区域ID
     */
    private Integer oldAdCodeMsgId;

    /**
     * 新区域ID
     */
    private Integer newAdCodeMsgId;
}
