package net.summerfarm.wnc.application.service.changeTask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.context.FenceChangeContext;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.application.service.changeTask.factory.FenceChangeTaskContextFactory;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsQueryDomainService;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private FenceChangeTaskContextFactory fenceChangeTaskContextFactory;
    @Resource
    private WncFenceChangeRecordsQueryDomainService wncFenceChangeRecordsQueryDomainService;
    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Autowired
    private FenceRepository fenceRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1.根据切仓任务ID查询切仓变更批次
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeId(waitOrderChangeHandleTask.getId());

        if (CollectionUtils.isEmpty(fenceChangeRecordsEntities)) {
            log.info("围栏变更记录为空，跳过处理，任务ID:{}", waitOrderChangeHandleTask.getId());
            return;
        }
        // 2.使用通用工厂构建围栏变更上下文
        String changeBatchNo = fenceChangeRecordsEntities.get(0).getChangeBatchNo();

        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaChangeWarehouseRecordsEntities = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByFenceChangeTaskIds(Collections.singletonList(waitOrderChangeHandleTask.getId()));
        // 这里需要从任务中获取城市区域变更记录，暂时使用空列表
        FenceChangeContext context = fenceChangeTaskContextFactory.buildFenceChangeContext(changeBatchNo, cityAreaChangeWarehouseRecordsEntities);
        if (context == null) {
            log.info("自定义围栏切仓订单任务ID:{} 无围栏变更上下文，直接完结任务", waitOrderChangeHandleTask.getId());
            return;
        }

        // 3.使用通用方法判断围栏变更类型
        FenceChangeTypeEnum changeType = wncFenceChangeRecordsQueryDomainService.determineCustomFenceChangeType(context.getBeforeFenceChangeRecords());

        // 4.分析变更类型和获取变更的城市区域信息
        List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords = context.getBeforeFenceChangeRecords();
        List<Integer> beforeStoreNos = new ArrayList<>();
        String city = context.getCity();
        String area = context.getArea();

        if (FenceChangeTypeEnum.CUSTOM_TO_CUSTOM.equals(changeType)) {
            beforeStoreNos = beforeFenceChangeRecords.stream().map(WncFenceChangeRecordsEntity::getFenceStoreNo).distinct().collect(Collectors.toList());
        } else if (FenceChangeTypeEnum.NONE_TO_CUSTOM.equals(changeType)) {
            // 不涉及切仓直接完结掉即可
            fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));
            return;
        } else if (FenceChangeTypeEnum.NORMAL_TO_CUSTOM.equals(changeType)) {
            List<WncFenceAreaChangeRecordsEntity> fenceAreaChangeRecords =  context.beforeAreaChangeRecordsGet();

            List<WncFenceAreaChangeRecordsEntity> matchedAreaChangeRecords = fenceAreaChangeRecords.stream()
                    .filter(e -> Objects.equals(e.getCity(), city) && Objects.equals(e.getArea(), area))
                    .collect(Collectors.toList());

            Integer fenceId = matchedAreaChangeRecords.get(0).getFenceId();
            beforeStoreNos = beforeFenceChangeRecords.stream().filter(e -> Objects.equals(e.getFenceId(), fenceId))
                    .map(WncFenceChangeRecordsEntity::getFenceStoreNo).collect(Collectors.toList());
        } else {
            log.error("\n自定义围栏切仓订单处理任务，围栏类型异常\n，changeBatchNo: {}", changeBatchNo);
        }

        // 5.根据变更类型和城市区域查询OFC订单，获取订单城市区域POI信息
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();

        for (Integer storeNo : beforeStoreNos) {
            FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                    .city(city)
                    .areas(Collections.singletonList(area))
                    .storeNo(storeNo)
                    .deliveryDateBegin(waitOrderChangeHandleTask.getExeTimePlus2Date()) // T+2的时间点
                    .build();
            List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);

            allFulfillmentOrders.addAll(fulfillmentOrders);
        }

        // 6.把订单数据存到表中
        List<FenceChangeTaskOrderEntity> saveFenceChangeTaskOrderList = allFulfillmentOrders.stream().map(FenceChangeTaskOrderConverter::dto2Entity).collect(Collectors.toList());
        fenceChangeTaskDomainService.saveBatchDetail(saveFenceChangeTaskOrderList);

        // 自定义围栏 -> 自定义围栏
        List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList = context.beforeAreaChangeRecordsGet();
        List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList = context.afterAreaChangeRecordsGet();

        // 7.根据表中数据，通过城市、区域、POI进行快照前后的匹配查询
        saveFenceChangeTaskOrderList.forEach(orderInfo ->{
            String orderCity = orderInfo.getCity();
            String orderArea = orderInfo.getArea();
            String orderPoi = orderInfo.getPoi();

            if (FenceChangeTypeEnum.CUSTOM_TO_CUSTOM.equals(changeType)) {

                // Es POI匹配历史围栏区域
                List<Integer> beforeAdCodeMsgIds = beforeAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                        .collect(Collectors.toList());

               Integer beforeAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(beforeAdCodeMsgIds, orderPoi);
               if (beforeAdCodeMsgId == null) {
                   fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "未匹配到旧的自定义围栏");
                   return;
               }

               // Es POI匹配新围栏区域【有效配送区域】
                List<Integer> afterAdCodeMsgIds = afterAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .filter(e -> Objects.equals(e.getAdCodeMsgDetailEntity().getStatus(), AdCodeMsgEnums.Status.VALID.getValue()))
                        .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                        .collect(Collectors.toList());

                Integer afterAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(afterAdCodeMsgIds, orderPoi);
                if (afterAdCodeMsgId == null) {
                    fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), "不在有效配送区域");
                    return;
                }

                // 旧围栏信息
                Map<Integer, FenceEntity> beforeAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
                FenceEntity beforeFenceEntity = beforeAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

                Integer beforeStoreNo = beforeFenceEntity.getStoreNo();
                Integer beforeFenceId = beforeFenceEntity.getId();
                Integer beforeAreaNo = beforeFenceEntity.getAreaNo();

                // 新围栏信息
                Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
                FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

                Integer afterStoreNo = afterFenceEntity.getStoreNo();
                Integer afterFenceId = afterFenceEntity.getId();
                Integer afterAreaNo = afterFenceEntity.getAreaNo();

                orderInfo.setId(orderInfo.getId());

                orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
                orderInfo.setOldFenceId(beforeFenceId);
                orderInfo.setOldStoreNo(beforeStoreNo);
                orderInfo.setOldAreaNo(beforeAreaNo);

                orderInfo.setNewAdCodeMsgId(afterAdCodeMsgId);
                orderInfo.setNewFenceId(afterFenceId);
                orderInfo.setNewStoreNo(afterStoreNo);
                orderInfo.setNewAreaNo(afterAreaNo);

                fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);

            } else {
                // 普通围栏 -> 自定义围栏

                // 匹配订单所属的旧围栏区域
                WncFenceAreaChangeRecordsEntity beforeMatchedAreaChangeRecord = beforeAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .findFirst()
                        .orElse(null);
                if(beforeMatchedAreaChangeRecord == null){
                    fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "未匹配到旧的围栏");
                    return;
                }

                Integer beforeAdCodeMsgId = beforeMatchedAreaChangeRecord.getAdCodeMsgId();
                Integer beforeFenceId = beforeMatchedAreaChangeRecord.getFenceId();
                // 旧围栏信息
                FenceEntity beforeFenceEntity = fenceRepository.queryById(beforeFenceId);
                if (beforeFenceEntity == null) {
                    fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), "未匹配查询到旧的围栏");
                    return;
                }

                // 新围栏信息
                // Es POI匹配新围栏区域【有效配送区域】
                List<Integer> afterAdCodeMsgIds = afterAreaChangeRecordsList.stream()
                        .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                        .filter(e -> Objects.equals(e.getAdCodeMsgDetailEntity().getStatus(), AdCodeMsgEnums.Status.VALID.getValue()))
                        .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                        .collect(Collectors.toList());

                Integer afterAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(afterAdCodeMsgIds, orderPoi);
                if (afterAdCodeMsgId == null) {
                    fenceChangeTaskDomainService.orderChangeNoNeedHandle(orderInfo.getId(), "不在有效配送区域");
                    return;
                }


                Integer beforeStoreNo = beforeFenceEntity.getStoreNo();
                Integer beforeAreaNo = beforeFenceEntity.getAreaNo();

                // 新围栏信息
                Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsgId));
                FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsgId);

                Integer afterStoreNo = afterFenceEntity.getStoreNo();
                Integer afterFenceId = afterFenceEntity.getId();
                Integer afterAreaNo = afterFenceEntity.getAreaNo();

                // 保存订单切围栏前后信息
                orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
                orderInfo.setOldFenceId(beforeFenceId);
                orderInfo.setOldStoreNo(beforeStoreNo);
                orderInfo.setOldAreaNo(beforeAreaNo);

                orderInfo.setNewAdCodeMsgId(afterAdCodeMsgId);
                orderInfo.setNewFenceId(afterFenceId);
                orderInfo.setNewStoreNo(afterStoreNo);
                orderInfo.setNewAreaNo(afterAreaNo);

                fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);
            }
        });

        // 9.进行切仓处理
        List<String> failOrders = new ArrayList<>();
        saveFenceChangeTaskOrderList.forEach(orderInfo -> {
            try {
                fenceChangeTaskDomainService.doFenceChangeOrderHandleNew(orderInfo);
            } catch (Exception e) {
                failOrders.add(orderInfo.getOuterOrderId());
                log.info("围栏切仓订单处理任务-履约单处理失败，等待失败订单重试，异常原因：{}", e.getMessage(), e);
                FenceChangeTaskOrderEntity updateOrderFail = orderInfo.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
                fenceChangeTaskDetailRepository.update(updateOrderFail);
            }
        });

        // 更新切仓任务状态
        fenceChangeTaskRepository.update(waitOrderChangeHandleTask.execute(FenceChangeTaskEnums.Status.COMPLETED));

        if (!failOrders.isEmpty()){
            //存在订单切仓失败 发送飞书消息通知
            fenceChangeTaskSender.sendOrderFailMsg(waitOrderChangeHandleTask, failOrders.size());
        }
    }







}
